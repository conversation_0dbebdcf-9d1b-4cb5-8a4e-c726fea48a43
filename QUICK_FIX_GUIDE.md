# 快速修复指南

## ✅ 已修复的问题

### 1. 环境变量错误
**问题**: `process is not defined` 错误
**原因**: Vite 使用 `import.meta.env` 而不是 `process.env`
**修复**: 已更新所有环境变量引用

### 2. 文件更新
- ✅ `src/components/Comment.jsx` - 修复环境变量引用
- ✅ `.env.example` - 更新为 Vite 格式
- ✅ 文档更新 - 反映正确的环境变量名

## 🚀 现在可以正常使用

### 环境变量格式 (Vite)
```bash
# .env 文件
VITE_SUPABASE_URL=http://127.0.0.1:54321
VITE_SUPABASE_ANON_KEY=your-anon-key
VITE_CLOUD_FUNCTION_URL=https://your-function-url (可选)
```

### 测试步骤
1. 访问 http://localhost:5173
2. 注册/登录用户
3. 创建文章
4. 添加评论
5. 测试认可功能

## 🔧 如果还有问题

### 清除缓存
```bash
# 停止开发服务器 (Ctrl+C)
# 清除 node_modules 和重新安装
rm -rf node_modules package-lock.json
npm install
npm run dev
```

### 检查浏览器控制台
- 打开开发者工具 (F12)
- 查看 Console 标签页
- 检查是否有其他错误

### 验证环境变量
```javascript
// 在浏览器控制台中运行
console.log('Supabase URL:', import.meta.env.VITE_SUPABASE_URL)
console.log('Cloud Function URL:', import.meta.env.VITE_CLOUD_FUNCTION_URL)
```

现在系统应该可以正常工作了！🎉
