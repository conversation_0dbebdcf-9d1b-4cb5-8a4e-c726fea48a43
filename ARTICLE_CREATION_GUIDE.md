# 文章创作与发布系统使用指南

## 🎯 功能概述

本系统实现了完整的文章创作与发布功能，包括：

- ✅ **富文本编辑器** - 基于 Editor.js 的现代化编辑体验
- ✅ **发文点数系统** - 每发布一篇文章消耗 1 个点数
- ✅ **匿名发布** - 支持匿名发布文章
- ✅ **实时验证** - 发布前检查点数和内容有效性
- ✅ **自动保存** - 编辑器内容实时保存到状态

## 🚀 如何测试

### 1. 注册和登录
1. 访问 http://localhost:5173
2. 点击"立即注册"创建新账户
3. 填写用户名、邮箱和密码
4. 注册成功后会自动获得 1 个发文点数

### 2. 创作文章
1. 在主页点击"创作文章"按钮
2. 输入文章标题（3-200字符）
3. 使用富文本编辑器编写内容：
   - 点击 "+" 按钮添加不同类型的内容块
   - 支持标题、段落、列表、引用、代码等
   - 可以拖拽调整内容块顺序
4. 选择是否匿名发布
5. 点击"发布文章"

### 3. 编辑器功能测试

#### 支持的内容类型：
- **标题** - 6个级别的标题
- **段落** - 普通文本段落
- **列表** - 有序和无序列表
- **引用** - 引用块和引用来源
- **代码** - 代码块
- **链接** - 自动链接识别
- **图片** - 图片上传（需要后端支持）
- **嵌入** - YouTube、CodePen等嵌入
- **表格** - 可编辑表格
- **分隔符** - 内容分隔线

#### 测试步骤：
1. 在编辑器中点击 "+" 按钮
2. 选择不同的内容类型
3. 输入测试内容
4. 验证内容正确显示和保存

## 🔧 开发者测试工具

### 浏览器控制台命令

在开发环境中，可以使用以下命令进行测试：

```javascript
// 给当前用户增加 5 个发文点数
window.testHelpers.addPostingPoints("user-id", 5)

// 重置用户发文点数为 10
window.testHelpers.resetPostingPoints("user-id", 10)

// 获取用户的所有文章
window.testHelpers.getUserPosts("user-id")

// 删除用户的所有文章（测试清理）
window.testHelpers.deleteUserPosts("user-id")
```

### 获取用户ID

```javascript
// 在浏览器控制台中获取当前用户ID
console.log("Current user ID:", window.supabase?.auth?.user?.id)
```

### 数据库直接操作

在 Supabase Studio (http://127.0.0.1:54323) 中：

```sql
-- 查看所有用户
SELECT id, email, created_at FROM auth.users;

-- 查看用户资料和点数
SELECT * FROM profiles;

-- 给用户增加点数
UPDATE profiles SET posting_points = 10 WHERE username = 'your-username';

-- 查看所有文章
SELECT p.*, pr.username 
FROM posts p 
JOIN profiles pr ON p.author_id = pr.id 
ORDER BY p.created_at DESC;

-- 删除所有测试文章
DELETE FROM posts WHERE title LIKE '%测试%';
```

## 📝 测试场景

### 场景 1: 正常发布流程
1. 用户有足够点数（≥1）
2. 输入有效标题和内容
3. 成功发布文章
4. 点数自动减少 1

### 场景 2: 点数不足
1. 用户点数为 0
2. 尝试发布文章
3. 系统显示错误提示
4. 发布按钮被禁用

### 场景 3: 内容验证
1. 空标题或内容
2. 标题过短（<3字符）或过长（>200字符）
3. 只有空的编辑器块
4. 系统显示相应错误信息

### 场景 4: 匿名发布
1. 勾选"匿名发布"选项
2. 发布文章
3. 文章显示为匿名（需要文章列表页面验证）

## 🐛 常见问题排查

### 编辑器不显示
- 检查 Editor.js 依赖是否正确安装
- 查看浏览器控制台是否有错误
- 确认 CSS 样式正确加载

### 发布失败
- 检查网络连接
- 验证 Supabase 连接状态
- 查看浏览器控制台错误信息
- 确认用户有足够的发文点数

### 点数系统异常
- 检查数据库触发器是否正常工作
- 验证 RLS 策略配置
- 查看 Supabase 日志

## 🔄 重置测试环境

如需重置测试环境：

```bash
# 重置数据库
supabase db reset

# 重新启动开发服务器
npm run dev
```

## 📊 数据结构

### 文章数据格式 (Editor.js JSON)

```json
{
  "time": 1672531200000,
  "blocks": [
    {
      "id": "header1",
      "type": "header",
      "data": {
        "text": "文章标题",
        "level": 2
      }
    },
    {
      "id": "paragraph1", 
      "type": "paragraph",
      "data": {
        "text": "文章内容段落..."
      }
    }
  ],
  "version": "2.28.2"
}
```

### 数据库表结构

- **posts**: 文章主表
  - `id`: 文章ID
  - `title`: 文章标题
  - `content`: Editor.js JSON 数据
  - `author_id`: 作者ID
  - `is_anonymous`: 是否匿名
  - `created_at`: 创建时间

- **profiles**: 用户资料表
  - `id`: 用户ID
  - `username`: 用户名
  - `posting_points`: 发文点数
  - `avatar_url`: 头像URL

## 🎉 下一步开发

完成文章创作系统后，可以继续开发：

1. **文章列表页面** - 显示所有已发布的文章
2. **文章详情页面** - 显示单篇文章内容
3. **评论系统** - 文章评论和作者认可功能
4. **用户个人页面** - 显示用户的所有文章
5. **搜索功能** - 按标题或内容搜索文章
6. **文章编辑** - 允许作者编辑已发布的文章
