-- Sample data for testing the writer platform

-- Note: In a real application, users would be created through Supabase Auth
-- This seed file is kept minimal since profiles and posts should be created
-- through the application's normal user registration and post creation flow

-- The database schema is ready for:
-- 1. User registration (creates profiles automatically via trigger)
-- 2. Post creation with rich content
-- 3. Comment system with approval functionality

-- To test the system:
-- 1. Register users through the application
-- 2. Create posts using the rich text editor
-- 3. Add comments and test the approval system

-- Database is ready for use!
