-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create profiles table (用户资料表)
-- This extends the auth.users table with additional profile information
CREATE TABLE IF NOT EXISTS profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    username TEXT UNIQUE NOT NULL,
    avatar_url TEXT,
    bio TEXT CHECK (LENGTH(bio) <= 500),
    posting_points INTEGER DEFAULT 1 NOT NULL CHECK (posting_points >= 0),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create posts table (文章表)
CREATE TABLE IF NOT EXISTS posts (
    id BIGSERIAL PRIMARY KEY,
    author_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL CHECK (LENGTH(title) > 0),
    content JSONB NOT NULL, -- Stores Editor.js JSON output
    is_anonymous BOOLEAN DEFAULT FALSE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create comments table (评论表)
CREATE TABLE IF NOT EXISTS comments (
    id BIGSERIAL PRIMARY KEY,
    post_id BIGINT REFERENCES posts(id) ON DELETE CASCADE NOT NULL,
    commenter_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    content TEXT NOT NULL CHECK (LENGTH(content) > 0),
    is_author_approved BOOLEAN DEFAULT FALSE NOT NULL, -- 核心功能：作者认可
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_posts_author_id ON posts(author_id);
CREATE INDEX IF NOT EXISTS idx_posts_created_at ON posts(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_comments_post_id ON comments(post_id);
CREATE INDEX IF NOT EXISTS idx_comments_commenter_id ON comments(commenter_id);
CREATE INDEX IF NOT EXISTS idx_comments_created_at ON comments(created_at);
CREATE INDEX IF NOT EXISTS idx_comments_approved ON comments(is_author_approved);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to automatically update updated_at columns
CREATE TRIGGER update_profiles_updated_at 
    BEFORE UPDATE ON profiles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_posts_updated_at 
    BEFORE UPDATE ON posts 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_comments_updated_at 
    BEFORE UPDATE ON comments 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to handle new user registration
-- This automatically creates a profile when a user signs up
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, username, avatar_url, bio)
    VALUES (
        NEW.id,
        COALESCE(NEW.raw_user_meta_data->>'username', 'user_' || substr(NEW.id::text, 1, 8)),
        NEW.raw_user_meta_data->>'avatar_url',
        NEW.raw_user_meta_data->>'bio'
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to automatically create profile on user signup
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to decrease posting points when creating a post
CREATE OR REPLACE FUNCTION public.decrease_posting_points()
RETURNS TRIGGER AS $$
BEGIN
    -- Check if user has enough posting points
    IF (SELECT posting_points FROM profiles WHERE id = NEW.author_id) < 1 THEN
        RAISE EXCEPTION 'Insufficient posting points';
    END IF;
    
    -- Decrease posting points by 1
    UPDATE profiles 
    SET posting_points = posting_points - 1 
    WHERE id = NEW.author_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to decrease posting points when a post is created
CREATE TRIGGER on_post_created
    BEFORE INSERT ON posts
    FOR EACH ROW EXECUTE FUNCTION public.decrease_posting_points();

-- Create system_messages table (系统消息表)
-- This stores system-wide messages and notifications for users
CREATE TABLE IF NOT EXISTS system_messages (
    id BIGSERIAL PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    message_type TEXT NOT NULL DEFAULT 'info' CHECK (message_type IN ('info', 'warning', 'success', 'error', 'announcement')),
    priority INTEGER DEFAULT 0 CHECK (priority >= 0 AND priority <= 10),
    is_active BOOLEAN DEFAULT true NOT NULL,
    target_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE, -- NULL means message is for all users
    is_read BOOLEAN DEFAULT false NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create indexes for system_messages
CREATE INDEX IF NOT EXISTS idx_system_messages_target_user_id ON system_messages(target_user_id);
CREATE INDEX IF NOT EXISTS idx_system_messages_is_active ON system_messages(is_active);
CREATE INDEX IF NOT EXISTS idx_system_messages_created_at ON system_messages(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_system_messages_priority ON system_messages(priority DESC);

-- Row Level Security (RLS) Policies

-- Enable RLS on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_messages ENABLE ROW LEVEL SECURITY;

-- Profiles policies
CREATE POLICY "Public profiles are viewable by everyone" ON profiles
    FOR SELECT USING (true);

CREATE POLICY "Users can insert their own profile" ON profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON profiles
    FOR UPDATE USING (auth.uid() = id);

-- Posts policies
CREATE POLICY "Posts are viewable by everyone" ON posts
    FOR SELECT USING (true);

CREATE POLICY "Authenticated users can create posts" ON posts
    FOR INSERT WITH CHECK (auth.role() = 'authenticated' AND auth.uid() = author_id);

CREATE POLICY "Users can update their own posts" ON posts
    FOR UPDATE USING (auth.uid() = author_id);

CREATE POLICY "Users can delete their own posts" ON posts
    FOR DELETE USING (auth.uid() = author_id);

-- Comments policies
CREATE POLICY "Comments are viewable by everyone" ON comments
    FOR SELECT USING (true);

CREATE POLICY "Authenticated users can create comments" ON comments
    FOR INSERT WITH CHECK (auth.role() = 'authenticated' AND auth.uid() = commenter_id);

CREATE POLICY "Users can update their own comments" ON comments
    FOR UPDATE USING (auth.uid() = commenter_id);

CREATE POLICY "Users can delete their own comments" ON comments
    FOR DELETE USING (auth.uid() = commenter_id);

-- Post authors can approve comments on their posts
CREATE POLICY "Post authors can approve comments" ON comments
    FOR UPDATE USING (
        auth.uid() IN (
            SELECT author_id FROM posts WHERE id = post_id
        )
    );

-- Create the approve_and_reward function for atomic comment approval and point reward
CREATE OR REPLACE FUNCTION approve_and_reward(comment_id_to_approve BIGINT)
RETURNS VOID AS $$
DECLARE
  the_commenter_id UUID;
  the_post_author_id UUID;
  current_user_id UUID;
BEGIN
  -- Get current authenticated user
  current_user_id := auth.uid();

  -- Get the commenter ID and post author ID
  SELECT
    c.commenter_id,
    p.author_id
  INTO
    the_commenter_id,
    the_post_author_id
  FROM comments c
  JOIN posts p ON c.post_id = p.id
  WHERE c.id = comment_id_to_approve;

  -- Check if the current user is the post author
  IF current_user_id != the_post_author_id THEN
    RAISE EXCEPTION 'Only the post author can approve comments';
  END IF;

  -- Check if comment exists
  IF the_commenter_id IS NULL THEN
    RAISE EXCEPTION 'Comment not found';
  END IF;

  -- Update comment status (set as approved)
  UPDATE comments
  SET is_author_approved = TRUE, updated_at = NOW()
  WHERE id = comment_id_to_approve;

  -- Award points to the commenter
  UPDATE profiles
  SET posting_points = posting_points + 1
  WHERE id = the_commenter_id;

END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION approve_and_reward(BIGINT) TO authenticated;

-- System messages policies
CREATE POLICY "Users can view their own messages and global messages" ON system_messages
    FOR SELECT USING (
        is_active = true
        AND (expires_at IS NULL OR expires_at > NOW())
        AND (target_user_id IS NULL OR target_user_id = auth.uid())
    );

CREATE POLICY "Users can mark their messages as read" ON system_messages
    FOR UPDATE USING (target_user_id = auth.uid() OR target_user_id IS NULL)
    WITH CHECK (target_user_id = auth.uid() OR target_user_id IS NULL);

-- Insert sample system messages
INSERT INTO system_messages (title, content, message_type, priority, is_active) VALUES
('欢迎来到回音角！', '欢迎加入回音角写作平台！您已获得 5 个发文点数，可以立即开始创作。通过发表优质评论获得作者认可，即可赚取更多发文点数。', 'success', 8, true),
('平台使用指南', '发布文章需要消耗 1 个发文点数。您可以通过发表有价值的评论并获得作者认可来赚取点数。每个被认可的评论将为您带来 1 个发文点数奖励。', 'info', 5, true),
('社区规范提醒', '请遵守社区规范，发布原创、有价值的内容。禁止发布违法、有害或侵犯他人权益的内容。让我们共同维护一个友好的创作环境。', 'warning', 3, true);

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;
