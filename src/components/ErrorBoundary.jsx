import React from 'react'

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props)
    this.state = { hasError: false, error: null, errorInfo: null }
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true }
  }

  componentDidCatch(error, errorInfo) {
    // Log the error to console
    console.error('ErrorBoundary caught an error:', error, errorInfo)
    
    this.setState({
      error: error,
      errorInfo: errorInfo
    })
  }

  render() {
    if (this.state.hasError) {
      // Fallback UI
      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="max-w-md mx-auto text-center">
            <div className="bg-red-50 border border-red-200 rounded-lg p-6">
              <h2 className="text-lg font-semibold text-red-800 mb-4">
                出现了错误
              </h2>
              <p className="text-red-700 mb-4">
                页面渲染时发生了错误，请刷新页面重试。
              </p>
              
              {process.env.NODE_ENV === 'development' && (
                <details className="text-left">
                  <summary className="cursor-pointer text-red-600 font-medium mb-2">
                    错误详情 (开发模式)
                  </summary>
                  <div className="bg-red-100 p-3 rounded text-xs">
                    <div className="mb-2">
                      <strong>错误:</strong>
                      <pre className="whitespace-pre-wrap">{this.state.error && this.state.error.toString()}</pre>
                    </div>
                    <div>
                      <strong>堆栈信息:</strong>
                      <pre className="whitespace-pre-wrap">{this.state.errorInfo?.componentStack || '暂无堆栈信息'}</pre>
                    </div>
                  </div>
                </details>
              )}
              
              <div className="mt-4 space-x-4">
                <button
                  onClick={() => window.location.reload()}
                  className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                >
                  刷新页面
                </button>
                <a
                  href="/"
                  className="inline-block px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
                >
                  返回首页
                </a>
              </div>
            </div>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

export default ErrorBoundary
