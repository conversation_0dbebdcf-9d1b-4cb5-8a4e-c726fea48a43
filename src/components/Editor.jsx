import React, { useEffect, useRef, useState, useImperativeHandle } from 'react'

const Editor = React.forwardRef(({
  data = null,
  onChange = () => {},
  placeholder = "开始写作...",
  readOnly = false,
  className = ""
}, ref) => {
  const editorRef = useRef(null)
  const editorInstanceRef = useRef(null)
  const [isReady, setIsReady] = useState(false)
  const [error, setError] = useState(null)

  useEffect(() => {
    let mounted = true

    // Initialize Editor.js with dynamic imports
    const initEditor = async () => {
      if (!editorInstanceRef.current && editorRef.current && mounted) {
        try {
          // Dynamic imports to avoid SSR issues and reduce bundle size
          const EditorJS = (await import('@editorjs/editorjs')).default
          const Header = (await import('@editorjs/header')).default
          const List = (await import('@editorjs/list')).default
          const Paragraph = (await import('@editorjs/paragraph')).default
          const Quote = (await import('@editorjs/quote')).default
          const Code = (await import('@editorjs/code')).default
          const Delimiter = (await import('@editorjs/delimiter')).default

          const editor = new EditorJS({
            holder: editorRef.current,
            readOnly: readOnly,
            placeholder: placeholder,
            data: data || {
              blocks: []
            },
            tools: {
              header: {
                class: Header,
                config: {
                  placeholder: '输入标题...',
                  levels: [1, 2, 3, 4, 5, 6],
                  defaultLevel: 2
                }
              },
              paragraph: {
                class: Paragraph,
                config: {
                  placeholder: '输入段落内容...'
                }
              },
              list: {
                class: List,
                config: {
                  defaultStyle: 'unordered'
                }
              },
              quote: {
                class: Quote,
                config: {
                  quotePlaceholder: '输入引用内容...',
                  captionPlaceholder: '引用来源'
                }
              },
              code: {
                class: Code,
                config: {
                  placeholder: '输入代码...'
                }
              },
              delimiter: Delimiter
            },
            onChange: async () => {
              if (editorInstanceRef.current && onChange && mounted) {
                try {
                  const outputData = await editorInstanceRef.current.save()
                  onChange(outputData)
                } catch (error) {
                  console.error('Editor save error:', error)
                }
              }
            },
            onReady: () => {
              if (mounted) {
                setIsReady(true)
                setError(null)
              }
            }
          })

          editorInstanceRef.current = editor
        } catch (err) {
          console.error('Error initializing editor:', err)
          if (mounted) {
            setError('编辑器初始化失败: ' + err.message)
          }
        }
      }
    }

    // Add a small delay to ensure DOM is ready
    const timer = setTimeout(initEditor, 100)

    // Cleanup function
    return () => {
      clearTimeout(timer)
      mounted = false
      if (editorInstanceRef.current && editorInstanceRef.current.destroy) {
        try {
          editorInstanceRef.current.destroy()
        } catch (err) {
          console.error('Error destroying editor:', err)
        }
        editorInstanceRef.current = null
      }
      setIsReady(false)
    }
  }, [data, onChange, placeholder, readOnly])

  // Method to get editor data
  const save = async () => {
    if (editorInstanceRef.current) {
      try {
        const outputData = await editorInstanceRef.current.save()
        return outputData
      } catch (error) {
        console.error('Error saving editor data:', error)
        throw error
      }
    }
    throw new Error('编辑器未初始化')
  }

  // Method to clear editor
  const clear = async () => {
    if (editorInstanceRef.current) {
      try {
        await editorInstanceRef.current.clear()
      } catch (error) {
        console.error('Error clearing editor:', error)
      }
    }
  }

  // Method to render editor data (for read-only mode)
  const render = async (data) => {
    if (editorInstanceRef.current) {
      try {
        await editorInstanceRef.current.render(data)
      } catch (error) {
        console.error('Error rendering editor data:', error)
      }
    }
  }

  // Expose methods to parent component
  useImperativeHandle(ref, () => ({
    save,
    clear,
    render,
    isReady
  }))

  return (
    <div className={`editor-wrapper ${className}`}>
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-xl text-sm mb-4 flex items-center gap-2">
          <svg className="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          {error}
        </div>
      )}
      <div
        ref={editorRef}
        className="editor-container min-h-[500px] border border-slate-200 rounded-2xl p-6 focus-within:border-blue-500 focus-within:ring-2 focus-within:ring-blue-500/20 transition-all duration-200 bg-white shadow-sm hover:shadow-md"
        style={{
          fontSize: '16px',
          lineHeight: '1.7'
        }}
      />
      {!isReady && !error && (
        <div className="flex items-center justify-center py-12">
          <div className="flex flex-col items-center gap-3">
            <div className="animate-spin rounded-full h-10 w-10 border-2 border-blue-600 border-t-transparent"></div>
            <span className="text-slate-600 font-medium">编辑器加载中...</span>
          </div>
        </div>
      )}
    </div>
  )
})

// Custom CSS for Editor.js styling
const editorStyles = `
  .ce-block__content,
  .ce-toolbar__content {
    max-width: none;
  }

  .ce-block {
    margin: 0.5em 0;
  }

  .ce-paragraph {
    line-height: 1.6;
  }

  .ce-header {
    margin: 1em 0 0.5em 0;
  }

  .ce-quote {
    border-left: 4px solid #e5e7eb;
    padding-left: 1rem;
    margin: 1em 0;
  }

  .ce-code {
    background-color: #f3f4f6;
    border-radius: 0.375rem;
    padding: 1rem;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  }

  .ce-list {
    margin: 0.5em 0;
  }

  .ce-table {
    margin: 1em 0;
  }

  .ce-delimiter {
    margin: 2em 0;
    text-align: center;
  }

  .ce-toolbar__plus {
    color: #6366f1;
  }

  .ce-toolbar__settings-btn {
    color: #6366f1;
  }

  .ce-popover {
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  }
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = editorStyles;
  document.head.appendChild(styleElement);
}

Editor.displayName = 'Editor'

export default Editor
