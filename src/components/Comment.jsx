import { useState } from 'react'
import { approveComment } from '../lib/supabaseClient'

// Configuration for cloud function (set this to your deployed function URL)
const CLOUD_FUNCTION_URL = import.meta.env.VITE_CLOUD_FUNCTION_URL || null
const USE_CLOUD_FUNCTION = !!CLOUD_FUNCTION_URL

const Comment = ({ comment, isPostAuthor, onApproved }) => {
  const [isApproving, setIsApproving] = useState(false)
  const [error, setError] = useState('')

  // Cloud function approval method
  const approveCommentViaCloudFunction = async (commentId) => {
    try {
      const response = await fetch(`${CLOUD_FUNCTION_URL}/approve-comment`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // Add authentication header if needed in production
          // 'Authorization': `Bearer ${userToken}`
        },
        body: JSON.stringify({ comment_id: commentId })
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.detail || 'Failed to approve comment')
      }

      const result = await response.json()
      return { data: result, error: null }
    } catch (error) {
      return { data: null, error: error.message }
    }
  }

  // Handle comment approval
  const handleApprove = async () => {
    try {
      setIsApproving(true)
      setError('')

      let result
      if (USE_CLOUD_FUNCTION) {
        // Use cloud function
        result = await approveCommentViaCloudFunction(comment.id)
      } else {
        // Use direct Supabase function
        result = await approveComment(comment.id)
      }

      if (result.error) {
        throw new Error(result.error)
      }

      // Call the callback to update the parent component
      onApproved()

    } catch (err) {
      console.error('Error approving comment:', err)
      setError(err.message || '认可评论失败，请重试')
    } finally {
      setIsApproving(false)
    }
  }

  return (
    <div className={`relative group transition-all duration-300 ${
      comment.is_author_approved
        ? 'bg-gradient-to-r from-emerald-50 to-teal-50 border border-emerald-200 shadow-lg shadow-emerald-100/50'
        : 'bg-white border border-slate-200 hover:border-slate-300 hover:shadow-md'
    } rounded-2xl p-6`}>

      {/* Approved Badge */}
      {comment.is_author_approved && (
        <div className="absolute -top-2 -right-2">
          <div className="w-8 h-8 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-full flex items-center justify-center shadow-lg">
            <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
        </div>
      )}

      {/* Comment Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-start space-x-4">
          {/* Avatar */}
          <div className="relative">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg">
              {comment.profiles?.avatar_url ? (
                <img
                  src={comment.profiles.avatar_url}
                  alt={comment.profiles.username}
                  className="w-12 h-12 rounded-full object-cover"
                />
              ) : (
                <span className="text-white font-semibold text-lg">
                  {comment.profiles?.username?.charAt(0)?.toUpperCase() || '?'}
                </span>
              )}
            </div>
            {comment.is_author_approved && (
              <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-emerald-500 rounded-full flex items-center justify-center">
                <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
                </svg>
              </div>
            )}
          </div>

          {/* User Info */}
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-1">
              <span className="font-semibold text-slate-900 text-lg">
                {comment.profiles?.username || '未知用户'}
              </span>
              {comment.is_author_approved && (
                <span className="inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs font-medium bg-emerald-100 text-emerald-700 border border-emerald-200">
                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  作者认可
                </span>
              )}
            </div>
            <div className="flex items-center gap-2 text-sm text-slate-500">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span>
                {new Date(comment.created_at).toLocaleString('zh-CN', {
                  month: 'short',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </span>
            </div>
          </div>
        </div>

        {/* Approve Button (only for post author) */}
        {isPostAuthor && !comment.is_author_approved && (
          <div className="flex flex-col items-end">
            <button
              onClick={handleApprove}
              disabled={isApproving}
              className="group relative inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-emerald-500 to-teal-600 text-white font-medium rounded-xl hover:from-emerald-600 hover:to-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
            >
              {isApproving ? (
                <>
                  <svg className="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span>认可中...</span>
                </>
              ) : (
                <>
                  <svg className="w-4 h-4 transition-transform group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span>认可评论</span>
                </>
              )}
            </button>
            {error && (
              <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-600 text-xs font-medium">{error}</p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Comment Content */}
      <div className="mt-4 mb-4">
        <div className="text-slate-700 leading-relaxed text-base">
          {comment.content}
        </div>
      </div>

      {/* Approval Status for Author */}
      {isPostAuthor && comment.is_author_approved && (
        <div className="mt-4 p-3 bg-emerald-50 border border-emerald-200 rounded-xl">
          <div className="flex items-center gap-2 text-sm text-emerald-700 font-medium">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
            </svg>
            <span>您已认可此评论，评论者获得了 1 个发文点数奖励</span>
          </div>
        </div>
      )}
    </div>
  )
}

export default Comment
