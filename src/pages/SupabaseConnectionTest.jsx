import { useState, useEffect } from 'react'
import { supabase } from '../lib/supabaseClient'

const SupabaseConnectionTest = () => {
  // Only show in development mode
  if (import.meta.env.PROD) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            页面不可用
          </h1>
          <p className="text-gray-600 mb-6">
            此页面仅在开发模式下可用。
          </p>
          <a
            href="/"
            className="inline-block px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            返回首页
          </a>
        </div>
      </div>
    )
  }

  // Initialize with environment variables immediately
  const envVars = {
    VITE_SUPABASE_URL: import.meta.env.VITE_SUPABASE_URL,
    VITE_SUPABASE_ANON_KEY: import.meta.env.VITE_SUPABASE_ANON_KEY ? 'SET' : 'NOT SET'
  }

  const [results, setResults] = useState({
    envVars,
    connectionTest: null,
    sessionTest: null,
    error: null
  })

  useEffect(() => {
    const runTests = async () => {
      console.log('Starting Supabase connection tests...')

      try {
        // Test 2: Basic connection test
        console.log('Testing basic Supabase connection...')
        const connectionStart = Date.now()
        const { data: connectionData, error: connectionError } = await supabase
          .from('profiles')
          .select('count')
          .limit(1)
        
        const connectionTime = Date.now() - connectionStart
        console.log('Connection test result:', { connectionData, connectionError, time: connectionTime })

        // Test 3: Session test
        console.log('Testing session retrieval...')
        const sessionStart = Date.now()
        const { data: sessionData, error: sessionError } = await supabase.auth.getSession()
        const sessionTime = Date.now() - sessionStart
        console.log('Session test result:', { session: !!sessionData.session, sessionError, time: sessionTime })

        setResults({
          envVars,
          connectionTest: {
            success: !connectionError,
            error: connectionError?.message,
            time: connectionTime,
            data: connectionData
          },
          sessionTest: {
            success: !sessionError,
            error: sessionError?.message,
            time: sessionTime,
            hasSession: !!sessionData.session,
            session: sessionData.session
          },
          error: null
        })

      } catch (err) {
        console.error('Test error:', err)
        setResults(prev => ({
          ...prev,
          envVars,
          error: err.message
        }))
      }
    }

    runTests()
  }, [])

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Supabase 连接测试</h1>
        
        <div className="space-y-6">
          {/* Environment Variables */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">环境变量</h2>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="font-medium">VITE_SUPABASE_URL:</span>
                <span className={results.envVars.VITE_SUPABASE_URL ? 'text-green-600' : 'text-red-600'}>
                  {results.envVars.VITE_SUPABASE_URL || '未设置'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">VITE_SUPABASE_ANON_KEY:</span>
                <span className={results.envVars.VITE_SUPABASE_ANON_KEY === 'SET' ? 'text-green-600' : 'text-red-600'}>
                  {results.envVars.VITE_SUPABASE_ANON_KEY === 'SET' ? '已设置' : '未设置'}
                </span>
              </div>
            </div>
          </div>

          {/* Connection Test */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">数据库连接测试</h2>
            {results.connectionTest ? (
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="font-medium">状态:</span>
                  <span className={results.connectionTest.success ? 'text-green-600' : 'text-red-600'}>
                    {results.connectionTest.success ? '成功' : '失败'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">响应时间:</span>
                  <span>{results.connectionTest.time}ms</span>
                </div>
                {results.connectionTest.error && (
                  <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded">
                    <span className="text-red-600 text-sm">{results.connectionTest.error}</span>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-gray-500">测试中...</div>
            )}
          </div>

          {/* Session Test */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">认证会话测试</h2>
            {results.sessionTest ? (
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="font-medium">状态:</span>
                  <span className={results.sessionTest.success ? 'text-green-600' : 'text-red-600'}>
                    {results.sessionTest.success ? '成功' : '失败'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">响应时间:</span>
                  <span>{results.sessionTest.time}ms</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">是否有会话:</span>
                  <span className={results.sessionTest.hasSession ? 'text-green-600' : 'text-gray-600'}>
                    {results.sessionTest.hasSession ? '是' : '否'}
                  </span>
                </div>
                {results.sessionTest.error && (
                  <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded">
                    <span className="text-red-600 text-sm">{results.sessionTest.error}</span>
                  </div>
                )}
                {results.sessionTest.session && (
                  <div className="mt-4">
                    <h4 className="font-medium mb-2">会话数据:</h4>
                    <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto">
                      {JSON.stringify(results.sessionTest.session, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-gray-500">测试中...</div>
            )}
          </div>

          {/* Error */}
          {results.error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-6">
              <h2 className="text-xl font-semibold text-red-800 mb-2">错误</h2>
              <p className="text-red-600">{results.error}</p>
            </div>
          )}

          {/* Navigation */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">导航</h2>
            <div className="space-x-4">
              <a
                href="/"
                className="inline-block px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                首页
              </a>
              <a
                href="/auth-debug-simple"
                className="inline-block px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
              >
                认证调试
              </a>
              <a
                href="/test-supabase"
                className="inline-block px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
              >
                旧版 Supabase 测试
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default SupabaseConnectionTest
