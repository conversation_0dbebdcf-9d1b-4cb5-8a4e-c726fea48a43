import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'

const CreatePostSimple = () => {
  const [title, setTitle] = useState('')
  const [content, setContent] = useState('')
  const [isAnonymous, setIsAnonymous] = useState(false)
  const [isPublishing, setIsPublishing] = useState(false)
  const [error, setError] = useState('')
  
  const navigate = useNavigate()
  const { user, profile } = useAuth()

  console.log('CreatePostSimple rendered', { user, profile })

  // Check if user has enough posting points
  const canPublish = profile?.posting_points > 0

  const handlePublish = async () => {
    setIsPublishing(true)
    setError('')

    try {
      if (!title.trim()) {
        setError('请输入文章标题')
        return
      }

      if (!content.trim()) {
        setError('请输入文章内容')
        return
      }

      if (!canPublish) {
        setError('发文点数不足，无法发布文章')
        return
      }

      // Simulate publishing
      console.log('Publishing post:', { title, content, isAnonymous })
      
      // For now, just show success
      alert('文章发布成功！（这是简化版本）')
      navigate('/', { replace: true })

    } catch (err) {
      console.error('Error publishing post:', err)
      setError(err.message || '发布文章时发生错误，请重试')
    } finally {
      setIsPublishing(false)
    }
  }

  const handleCancel = () => {
    navigate('/', { replace: true })
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">请先登录</h2>
          <p className="text-gray-600">您需要登录后才能创作文章</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold text-gray-900">
                创作新文章 (简化版)
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-indigo-600 font-medium">
                发文点数: {profile?.posting_points || 0}
              </span>
              <button
                onClick={handleCancel}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              >
                取消
              </button>
              <button
                onClick={handlePublish}
                disabled={isPublishing || !canPublish}
                className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isPublishing ? '发布中...' : '发布文章'}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-lg shadow p-6">
          {/* Title Input */}
          <div className="mb-6">
            <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
              文章标题 *
            </label>
            <input
              type="text"
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="请输入文章标题..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-lg"
              disabled={isPublishing}
            />
          </div>

          {/* Content Input */}
          <div className="mb-6">
            <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-2">
              文章内容 *
            </label>
            <textarea
              id="content"
              value={content}
              onChange={(e) => setContent(e.target.value)}
              placeholder="请输入文章内容..."
              rows={10}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              disabled={isPublishing}
            />
          </div>

          {/* Anonymous Option */}
          <div className="mb-6">
            <div className="flex items-center">
              <input
                id="anonymous"
                type="checkbox"
                checked={isAnonymous}
                onChange={(e) => setIsAnonymous(e.target.checked)}
                className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                disabled={isPublishing}
              />
              <label htmlFor="anonymous" className="ml-2 block text-sm text-gray-700">
                匿名发布
              </label>
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm">
              {error}
            </div>
          )}

          {/* Posting Points Warning */}
          {!canPublish && (
            <div className="mb-6 bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded-md text-sm">
              <div className="flex">
                <svg className="flex-shrink-0 h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-yellow-800">
                    发文点数不足
                  </h3>
                  <p className="mt-1 text-sm text-yellow-700">
                    您当前的发文点数为 {profile?.posting_points || 0}，需要至少 1 个点数才能发布文章。
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Debug Info */}
          <div className="mt-6 p-4 bg-gray-50 border border-gray-200 rounded-md">
            <h4 className="text-sm font-medium text-gray-800 mb-2">调试信息</h4>
            <div className="text-xs text-gray-600 space-y-1">
              <p><strong>用户ID:</strong> {user?.id || 'N/A'}</p>
              <p><strong>用户邮箱:</strong> {user?.email || 'N/A'}</p>
              <p><strong>用户名:</strong> {profile?.username || 'N/A'}</p>
              <p><strong>发文点数:</strong> {profile?.posting_points || 0}</p>
              <p><strong>可以发布:</strong> {canPublish ? '是' : '否'}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CreatePostSimple
