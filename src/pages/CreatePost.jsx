import React, { useState, useRef } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import { supabase } from '../lib/supabaseClient'
import Editor from '../components/Editor'

const CreatePost = () => {
  const [title, setTitle] = useState('')
  const [isAnonymous, setIsAnonymous] = useState(false)
  const [isPublishing, setIsPublishing] = useState(false)
  const [error, setError] = useState('')
  const [editorData, setEditorData] = useState(null)

  const editorRef = useRef(null)
  const navigate = useNavigate()

  const { user, profile, updateProfile } = useAuth()

  // Check if user has enough posting points
  const canPublish = profile?.posting_points > 0

  const handleEditorChange = (data) => {
    setEditorData(data)
    // Clear error when user starts typing
    if (error) setError('')
  }

  const validatePost = () => {
    if (!title.trim()) {
      return '请输入文章标题'
    }

    if (title.trim().length < 3) {
      return '标题至少需要3个字符'
    }

    if (title.trim().length > 200) {
      return '标题不能超过200个字符'
    }

    if (!editorData || !editorData.blocks || editorData.blocks.length === 0) {
      return '请输入文章内容'
    }

    // Check if there's actual content (not just empty blocks)
    const hasContent = editorData.blocks.some(block => {
      if (block.type === 'paragraph' && block.data.text && block.data.text.trim()) {
        return true
      }
      if (block.type === 'header' && block.data.text && block.data.text.trim()) {
        return true
      }
      if (block.type === 'list' && block.data.items && block.data.items.length > 0) {
        return true
      }
      if (block.type === 'quote' && block.data.text && block.data.text.trim()) {
        return true
      }
      if (block.type === 'code' && block.data.code && block.data.code.trim()) {
        return true
      }
      return false
    })

    if (!hasContent) {
      return '请输入有效的文章内容'
    }

    if (!canPublish) {
      return '发文点数不足，无法发布文章'
    }

    return null
  }

  const handlePublish = async () => {
    setIsPublishing(true)
    setError('')

    try {
      // Get latest editor data
      const currentEditorData = editorRef.current ? await editorRef.current.save() : editorData

      // Validate the post
      const validationError = validatePost()
      if (validationError) {
        setError(validationError)
        setIsPublishing(false)
        return
      }

      // Check posting points again (in case it changed)
      const { data: currentProfile, error: profileError } = await supabase
        .from('profiles')
        .select('posting_points')
        .eq('id', user.id)
        .single()

      if (profileError) {
        throw new Error('无法获取用户信息')
      }

      if (currentProfile.posting_points < 1) {
        setError('发文点数不足，无法发布文章')
        setIsPublishing(false)
        return
      }

      // Create the post
      const { error: postError } = await supabase
        .from('posts')
        .insert([
          {
            title: title.trim(),
            content: currentEditorData,
            author_id: user.id,
            is_anonymous: isAnonymous
          }
        ])
        .select()
        .single()

      if (postError) {
        throw new Error(postError.message || '发布文章失败')
      }

      // Update local profile state to reflect the decreased posting points
      // The database trigger will automatically decrease the points
      await updateProfile({ posting_points: currentProfile.posting_points - 1 })

      // Success! Navigate to the post or posts list
      alert('文章发布成功！')
      navigate('/', { replace: true })

    } catch (err) {
      console.error('Error publishing post:', err)
      setError(err.message || '发布文章时发生错误，请重试')
    } finally {
      setIsPublishing(false)
    }
  }

  const handleCancel = () => {
    if (title || (editorData && editorData.blocks && editorData.blocks.length > 0)) {
      if (window.confirm('确定要放弃当前编辑的内容吗？')) {
        navigate('/', { replace: true })
      }
    } else {
      navigate('/', { replace: true })
    }
  }

  // Add error boundary for this component
  if (!user) {
    console.log('CreatePost: No user found')
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">加载中...</h2>
          <p className="text-gray-600">正在获取用户信息</p>
        </div>
      </div>
    )
  }

  try {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100">
        {/* Modern Header */}
        <header className="sticky top-0 z-50 bg-white/80 backdrop-blur-md border-b border-slate-200/60">
          <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                  </svg>
                </div>
                <h1 className="text-xl font-bold text-slate-900">
                  创作新文章
                </h1>
              </div>

              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2 px-3 py-1 bg-blue-50 rounded-full">
                  <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                  </svg>
                  <span className="text-sm text-blue-700 font-medium">
                    发文点数: {profile?.posting_points || 0}
                  </span>
                </div>

                <button
                  onClick={handleCancel}
                  className="px-4 py-2 text-sm font-medium text-slate-700 bg-white border border-slate-300 rounded-lg hover:bg-slate-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                >
                  取消
                </button>

                <button
                  onClick={handlePublish}
                  disabled={isPublishing || !canPublish}
                  className="inline-flex items-center gap-2 px-6 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-purple-600 border border-transparent rounded-lg hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl"
                >
                  {isPublishing ? (
                    <>
                      <svg className="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      发布中...
                    </>
                  ) : (
                    <>
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                      </svg>
                      发布文章
                    </>
                  )}
              </button>
              </div>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <div className="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
          <div className="bg-white rounded-2xl shadow-xl shadow-slate-200/50 overflow-hidden">
            <div className="p-8 sm:p-12">
              {/* Title Input */}
              <div className="mb-8">
                <label htmlFor="title" className="block text-lg font-semibold text-slate-900 mb-3">
                  文章标题 *
                </label>
                <input
                  type="text"
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="请输入一个吸引人的标题..."
                  className="w-full px-4 py-3 border border-slate-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-xl font-medium bg-slate-50/50 hover:bg-white transition-all duration-200"
                  disabled={isPublishing}
                />
                <div className="flex justify-between items-center mt-2">
                  <p className="text-sm text-slate-500">
                    好的标题能吸引更多读者
                  </p>
                  <p className="text-sm text-slate-400">
                    {title.length}/200
                  </p>
                </div>
              </div>

              {/* Anonymous Option */}
              <div className="mb-8">
                <div className="flex items-start gap-3 p-4 bg-slate-50 rounded-xl border border-slate-200">
                  <input
                    id="anonymous"
                    type="checkbox"
                    checked={isAnonymous}
                    onChange={(e) => setIsAnonymous(e.target.checked)}
                    className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded"
                    disabled={isPublishing}
                  />
                  <div className="flex-1">
                    <label htmlFor="anonymous" className="block text-sm font-medium text-slate-900">
                      匿名发布
                    </label>
                    <p className="mt-1 text-xs text-slate-500">
                      匿名发布后，其他用户将无法看到您的用户名，但您仍可以管理文章
                    </p>
                  </div>
                </div>
              </div>

              {/* Editor */}
              <div className="mb-8">
                <label className="block text-lg font-semibold text-slate-900 mb-3">
                  文章内容 *
                </label>
                <Editor
                  ref={editorRef}
                  onChange={handleEditorChange}
                  placeholder="开始写作您的精彩内容..."
                  className="min-h-[500px]"
                />
              </div>

              {/* Error Message */}
              {error && (
                <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-xl text-sm flex items-center gap-2">
                  <svg className="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  {error}
                </div>
              )}

              {/* Posting Points Warning */}
              {!canPublish && (
                <div className="mb-6 bg-amber-50 border border-amber-200 text-amber-700 px-4 py-3 rounded-xl text-sm">
                  <div className="flex items-start gap-2">
                    <svg className="flex-shrink-0 w-5 h-5 text-amber-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                    <div>
                      <h3 className="text-sm font-semibold text-amber-800">
                        发文点数不足
                      </h3>
                      <p className="mt-1 text-sm text-amber-700">
                        您当前的发文点数为 {profile?.posting_points || 0}，需要至少 1 个点数才能发布文章。
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Help Text */}
              <div className="text-sm text-slate-500 bg-slate-50 p-4 rounded-xl border border-slate-200">
                <h4 className="font-semibold mb-3 text-slate-700 flex items-center gap-2">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  编辑器使用提示
                </h4>
                <ul className="list-disc list-inside space-y-1 text-slate-600">
                  <li>点击 "+" 按钮添加不同类型的内容块</li>
                  <li>支持标题、段落、列表、引用、代码、链接等多种格式</li>
                  <li>可以拖拽调整内容块的顺序</li>
                  <li>发布文章将消耗 1 个发文点数</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  } catch (error) {
    console.error('CreatePost render error:', error)
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-red-600 mb-2">渲染错误</h2>
          <p className="text-gray-600 mb-4">创建文章页面渲染失败</p>
          <div className="text-sm text-red-500">
            {error.message}
          </div>
          <button
            onClick={() => navigate('/')}
            className="mt-4 px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700"
          >
            返回首页
          </button>
        </div>
      </div>
    )
  }
}

export default CreatePost
