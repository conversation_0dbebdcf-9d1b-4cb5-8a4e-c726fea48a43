// Test helper functions for development
import { supabase } from '../lib/supabaseClient'

/**
 * Add posting points to a user (for testing purposes)
 * @param {string} userId - User ID
 * @param {number} points - Number of points to add
 * @returns {Promise<{success: boolean, error?: string}>}
 */
export const addPostingPoints = async (userId, points = 5) => {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .update({ 
        posting_points: supabase.raw(`posting_points + ${points}`)
      })
      .eq('id', userId)
      .select()
      .single()

    if (error) {
      return { success: false, error: error.message }
    }

    return { success: true, data }
  } catch (err) {
    return { success: false, error: err.message }
  }
}

/**
 * Reset posting points for a user (for testing purposes)
 * @param {string} userId - User ID
 * @param {number} points - Number of points to set (default: 1)
 * @returns {Promise<{success: boolean, error?: string}>}
 */
export const resetPostingPoints = async (userId, points = 1) => {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .update({ posting_points: points })
      .eq('id', userId)
      .select()
      .single()

    if (error) {
      return { success: false, error: error.message }
    }

    return { success: true, data }
  } catch (err) {
    return { success: false, error: err.message }
  }
}

/**
 * Get all posts by a user
 * @param {string} userId - User ID
 * @returns {Promise<{data, error}>}
 */
export const getUserPosts = async (userId) => {
  return await supabase
    .from('posts')
    .select(`
      *,
      profiles:author_id (
        username,
        avatar_url
      )
    `)
    .eq('author_id', userId)
    .order('created_at', { ascending: false })
}

/**
 * Delete all posts by a user (for testing cleanup)
 * @param {string} userId - User ID
 * @returns {Promise<{success: boolean, error?: string}>}
 */
export const deleteUserPosts = async (userId) => {
  try {
    const { error } = await supabase
      .from('posts')
      .delete()
      .eq('author_id', userId)

    if (error) {
      return { success: false, error: error.message }
    }

    return { success: true }
  } catch (err) {
    return { success: false, error: err.message }
  }
}

// Development helper: Add these functions to window for easy testing in browser console
if (typeof window !== 'undefined' && import.meta.env.DEV) {
  window.testHelpers = {
    addPostingPoints,
    resetPostingPoints,
    getUserPosts,
    deleteUserPosts
  }
  
  console.log('Test helpers available at window.testHelpers')
  console.log('Example usage:')
  console.log('  window.testHelpers.addPostingPoints("user-id", 5)')
  console.log('  window.testHelpers.resetPostingPoints("user-id", 10)')
}
