import { createClient } from '@supabase/supabase-js'

// Get environment variables
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

// Validate environment variables
if (!supabaseUrl) {
  throw new Error('Missing VITE_SUPABASE_URL environment variable')
}

if (!supabaseAnonKey) {
  throw new Error('Missing VITE_SUPABASE_ANON_KEY environment variable')
}

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    // Configure auth settings
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    // Storage for auth tokens (localStorage by default)
    storage: window.localStorage,
  },
  // Configure realtime settings
  realtime: {
    params: {
      eventsPerSecond: 10,
    },
  },
})

// Helper functions for common operations

/**
 * Get current user session
 * @returns {Promise<{data: {session}, error}>}
 */
export const getCurrentSession = async () => {
  return await supabase.auth.getSession()
}

/**
 * Get current user
 * @returns {Promise<{data: {user}, error}>}
 */
export const getCurrentUser = async () => {
  return await supabase.auth.getUser()
}

/**
 * Sign up with email and password
 * @param {string} email 
 * @param {string} password 
 * @param {object} metadata - Additional user metadata
 * @returns {Promise<{data, error}>}
 */
export const signUp = async (email, password, metadata = {}) => {
  return await supabase.auth.signUp({
    email,
    password,
    options: {
      data: metadata
    }
  })
}

/**
 * Sign in with email and password
 * @param {string} email 
 * @param {string} password 
 * @returns {Promise<{data, error}>}
 */
export const signIn = async (email, password) => {
  return await supabase.auth.signInWithPassword({
    email,
    password
  })
}

/**
 * Sign out current user
 * @returns {Promise<{error}>}
 */
export const signOut = async () => {
  return await supabase.auth.signOut()
}

/**
 * Listen to auth state changes
 * @param {function} callback - Callback function to handle auth state changes
 * @returns {object} - Subscription object with unsubscribe method
 */
export const onAuthStateChange = (callback) => {
  return supabase.auth.onAuthStateChange(callback)
}

// Database helper functions

/**
 * Get user profile by user ID
 * @param {string} userId 
 * @returns {Promise<{data, error}>}
 */
export const getUserProfile = async (userId) => {
  return await supabase
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .single()
}

/**
 * Update user profile
 * @param {string} userId 
 * @param {object} updates 
 * @returns {Promise<{data, error}>}
 */
export const updateUserProfile = async (userId, updates) => {
  return await supabase
    .from('profiles')
    .update(updates)
    .eq('id', userId)
    .select()
    .single()
}

/**
 * Create a new post
 * @param {object} postData 
 * @returns {Promise<{data, error}>}
 */
export const createPost = async (postData) => {
  return await supabase
    .from('posts')
    .insert(postData)
    .select()
    .single()
}

/**
 * Get posts with author information
 * @param {object} options - Query options (limit, offset, etc.)
 * @returns {Promise<{data, error}>}
 */
export const getPosts = async (options = {}) => {
  let query = supabase
    .from('posts')
    .select(`
      *,
      profiles:author_id (
        username,
        avatar_url
      )
    `)
    .order('created_at', { ascending: false })

  if (options.limit) {
    query = query.limit(options.limit)
  }

  if (options.offset) {
    query = query.range(options.offset, options.offset + (options.limit || 10) - 1)
  }

  return await query
}

/**
 * Get a single post by ID
 * @param {number} postId 
 * @returns {Promise<{data, error}>}
 */
export const getPost = async (postId) => {
  return await supabase
    .from('posts')
    .select(`
      *,
      profiles:author_id (
        username,
        avatar_url
      )
    `)
    .eq('id', postId)
    .single()
}

/**
 * Get comments for a post
 * @param {number} postId 
 * @returns {Promise<{data, error}>}
 */
export const getComments = async (postId) => {
  return await supabase
    .from('comments')
    .select(`
      *,
      profiles:commenter_id (
        username,
        avatar_url
      )
    `)
    .eq('post_id', postId)
    .order('created_at', { ascending: true })
}

/**
 * Create a new comment
 * @param {object} commentData
 * @returns {Promise<{data, error}>}
 */
export const createComment = async (commentData) => {
  return await supabase
    .from('comments')
    .insert(commentData)
    .select(`
      *,
      profiles:commenter_id (
        username,
        avatar_url
      )
    `)
    .single()
}

/**
 * Approve a comment (only by post author) and reward the commenter
 * @param {number} commentId
 * @returns {Promise<{data, error}>}
 */
export const approveComment = async (commentId) => {
  try {
    const { error } = await supabase.rpc('approve_and_reward', {
      comment_id_to_approve: commentId
    })

    if (error) {
      return { data: null, error: error.message }
    }

    return { data: { success: true }, error: null }
  } catch (err) {
    return { data: null, error: err.message }
  }
}

/**
 * Check if user has enough posting points
 * @param {string} userId
 * @returns {Promise<{hasPoints: boolean, currentPoints: number, error}>}
 */
export const checkPostingPoints = async (userId) => {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('posting_points')
      .eq('id', userId)
      .single()

    if (error) {
      return { hasPoints: false, currentPoints: 0, error: error.message }
    }

    return {
      hasPoints: data.posting_points > 0,
      currentPoints: data.posting_points,
      error: null
    }
  } catch (err) {
    return { hasPoints: false, currentPoints: 0, error: err.message }
  }
}

/**
 * Create a post with posting points validation
 * @param {object} postData
 * @returns {Promise<{data, error}>}
 */
export const createPostWithValidation = async (postData) => {
  try {
    // First check if user has enough points
    const pointsCheck = await checkPostingPoints(postData.author_id)

    if (!pointsCheck.hasPoints) {
      return {
        data: null,
        error: pointsCheck.error || 'Insufficient posting points'
      }
    }

    // Create the post (database trigger will handle point deduction)
    const { data, error } = await supabase
      .from('posts')
      .insert(postData)
      .select(`
        *,
        profiles:author_id (
          username,
          avatar_url
        )
      `)
      .single()

    return { data, error }
  } catch (err) {
    return { data: null, error: err.message }
  }
}

/**
 * Get posts with pagination
 * @param {object} options - Query options
 * @returns {Promise<{data, error, count}>}
 */
export const getPostsWithPagination = async (options = {}) => {
  const {
    limit = 10,
    offset = 0,
    orderBy = 'created_at',
    ascending = false,
    authorId = null
  } = options

  let query = supabase
    .from('posts')
    .select(`
      *,
      profiles:author_id (
        username,
        avatar_url
      )
    `, { count: 'exact' })
    .order(orderBy, { ascending })

  if (authorId) {
    query = query.eq('author_id', authorId)
  }

  if (limit) {
    query = query.range(offset, offset + limit - 1)
  }

  return await query
}

/**
 * Delete a post (only by author)
 * @param {number} postId
 * @param {string} authorId
 * @returns {Promise<{data, error}>}
 */
export const deletePost = async (postId, authorId) => {
  return await supabase
    .from('posts')
    .delete()
    .eq('id', postId)
    .eq('author_id', authorId)
}

/**
 * Update a post (only by author)
 * @param {number} postId
 * @param {string} authorId
 * @param {object} updates
 * @returns {Promise<{data, error}>}
 */
export const updatePost = async (postId, authorId, updates) => {
  return await supabase
    .from('posts')
    .update(updates)
    .eq('id', postId)
    .eq('author_id', authorId)
    .select(`
      *,
      profiles:author_id (
        username,
        avatar_url
      )
    `)
    .single()
}

/**
 * Get comments received by a user (comments on their posts)
 * @param {string} userId - The user ID whose posts' comments to fetch
 * @param {object} options - Query options (limit, offset, etc.)
 * @returns {Promise<{data, error, count}>}
 */
export const getCommentsReceivedByUser = async (userId, options = {}) => {
  const {
    limit = 10,
    offset = 0,
    orderBy = 'created_at',
    ascending = false
  } = options

  let query = supabase
    .from('comments')
    .select(`
      *,
      profiles:commenter_id (
        username,
        avatar_url
      ),
      posts!inner (
        id,
        title,
        author_id
      )
    `, { count: 'exact' })
    .eq('posts.author_id', userId)
    .order(orderBy, { ascending })

  if (limit) {
    query = query.range(offset, offset + limit - 1)
  }

  return await query
}

/**
 * Get comments made by a user
 * @param {string} userId - The user ID whose comments to fetch
 * @param {object} options - Query options (limit, offset, etc.)
 * @returns {Promise<{data, error, count}>}
 */
export const getCommentsMadeByUser = async (userId, options = {}) => {
  const {
    limit = 10,
    offset = 0,
    orderBy = 'created_at',
    ascending = false
  } = options

  let query = supabase
    .from('comments')
    .select(`
      *,
      posts!inner (
        id,
        title,
        author_id,
        profiles:author_id (
          username,
          avatar_url
        )
      )
    `, { count: 'exact' })
    .eq('commenter_id', userId)
    .order(orderBy, { ascending })

  if (limit) {
    query = query.range(offset, offset + limit - 1)
  }

  return await query
}

/**
 * Get detailed posting points information for a user
 * @param {string} userId - The user ID
 * @returns {Promise<{data, error}>}
 */
export const getPostingPointsDetails = async (userId) => {
  try {
    // Get current profile with posting points
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('posting_points, created_at')
      .eq('id', userId)
      .single()

    if (profileError) {
      return { data: null, error: profileError }
    }

    // Get posts count (each post costs 1 point)
    const { count: postsCount, error: postsError } = await supabase
      .from('posts')
      .select('*', { count: 'exact', head: true })
      .eq('author_id', userId)

    if (postsError) {
      return { data: null, error: postsError }
    }

    // Get approved comments count (each approved comment earns 1 point)
    const { count: approvedCommentsCount, error: commentsError } = await supabase
      .from('comments')
      .select('*', { count: 'exact', head: true })
      .eq('commenter_id', userId)
      .eq('is_author_approved', true)

    if (commentsError) {
      return { data: null, error: commentsError }
    }

    // Calculate points breakdown
    const initialPoints = 5 // New users start with 5 points
    const pointsEarned = approvedCommentsCount || 0
    const pointsSpent = postsCount || 0
    const currentPoints = profile.posting_points || 0

    return {
      data: {
        currentPoints,
        initialPoints,
        pointsEarned,
        pointsSpent,
        postsCount: postsCount || 0,
        approvedCommentsCount: approvedCommentsCount || 0,
        joinDate: profile.created_at
      },
      error: null
    }
  } catch (err) {
    return { data: null, error: err.message }
  }
}

/**
 * Delete a comment (only by commenter or post author)
 * @param {number} commentId
 * @param {string} userId - Current user ID
 * @returns {Promise<{data, error}>}
 */
export const deleteComment = async (commentId, userId) => {
  // First check if user is the commenter or post author
  const { data: comment, error: fetchError } = await supabase
    .from('comments')
    .select(`
      *,
      posts!inner (
        author_id
      )
    `)
    .eq('id', commentId)
    .single()

  if (fetchError) {
    return { data: null, error: fetchError.message }
  }

  // Check if user is commenter or post author
  const isCommenter = comment.commenter_id === userId
  const isPostAuthor = comment.posts.author_id === userId

  if (!isCommenter && !isPostAuthor) {
    return { data: null, error: '您没有权限删除此评论' }
  }

  return await supabase
    .from('comments')
    .delete()
    .eq('id', commentId)
}

export default supabase
