
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { AuthProvider, useAuth } from './contexts/AuthContext'
import ErrorBoundary from './components/ErrorBoundary'
import Welcome from './pages/Welcome'
import Dashboard from './pages/Dashboard'
import Login from './pages/Login'
import Register from './pages/Register'
import CreatePost from './pages/CreatePost'
import CreatePostSimple from './pages/CreatePostSimple'
import CreatePostMinimal from './pages/CreatePostMinimal'
import PostDetail from './pages/PostDetail'
import SupabaseConnectionTest from './pages/SupabaseConnectionTest'
import './App.css'

// Import test helpers for development
if (import.meta.env.DEV) {
  import('./utils/testHelpers')
}

// Loading component
const LoadingSpinner = () => (
  <div className="min-h-screen flex items-center justify-center">
    <div className="text-center">
      <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600 mx-auto mb-4"></div>
      <p className="text-gray-600">加载中...</p>
    </div>
  </div>
)

// Protected Route component
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, loading } = useAuth()

  if (loading) {
    return <LoadingSpinner />
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />
  }

  return children
}

// Smart Home Route component (shows Welcome or Dashboard based on auth)
const HomeRoute = () => {
  const { isAuthenticated, loading } = useAuth()

  if (loading) {
    return <LoadingSpinner />
  }

  if (isAuthenticated) {
    return <Dashboard />
  } else {
    return <Welcome />
  }
}

// Public Route component (redirect to home if already authenticated)
const PublicRoute = ({ children }) => {
  const { isAuthenticated, loading } = useAuth()

  if (loading) {
    return <LoadingSpinner />
  }

  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />
  }

  return children
}



// Main App component
function App() {
  return (
    <ErrorBoundary>
      <AuthProvider>
        <Router>
          <div className="min-h-screen bg-gray-50">
            <Routes>
              {/* Public routes */}
              <Route
                path="/welcome"
                element={<Welcome />}
              />
              <Route
                path="/login"
                element={
                  <PublicRoute>
                    <Login />
                  </PublicRoute>
                }
              />
              <Route
                path="/register"
                element={
                  <PublicRoute>
                    <Register />
                  </PublicRoute>
                }
              />

              {/* Home route - smart routing based on auth */}
              <Route
                path="/"
                element={<HomeRoute />}
              />

              {/* Protected routes - require authentication */}
              <Route
                path="/dashboard"
                element={
                  <ProtectedRoute>
                    <Dashboard />
                  </ProtectedRoute>
                }
              />

              {/* Post creation routes */}
              <Route
                path="/create-post"
                element={
                  <ProtectedRoute>
                    <CreatePost />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/create-post-simple"
                element={
                  <ProtectedRoute>
                    <CreatePostSimple />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/create-post-minimal"
                element={
                  <ProtectedRoute>
                    <CreatePostMinimal />
                  </ProtectedRoute>
                }
              />

              {/* Post detail route */}
              <Route
                path="/post/:id"
                element={<PostDetail />}
              />

              {/* Development only routes */}
              {import.meta.env.DEV && (
                <Route
                  path="/supabase-connection-test"
                  element={<SupabaseConnectionTest />}
                />
              )}

              {/* Catch all route - redirect unknown paths to home */}
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
        </div>
      </Router>
    </AuthProvider>
  </ErrorBoundary>
  )
}

export default App
