import { createContext, useContext, useEffect, useState } from 'react'
import { supabase } from '../lib/supabaseClient'

// Create Auth Context
const AuthContext = createContext({})

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// Auth Provider Component
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null)
  const [profile, setProfile] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  // Get user profile from database with fallback to in-memory profile
  const fetchUserProfile = async (userId, userEmail) => {
    try {
      // Try to fetch from database with timeout
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Profile fetch timeout')), 3000)
      })

      const queryPromise = supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()

      const { data, error } = await Promise.race([queryPromise, timeoutPromise])

      if (error) {
        // If profile doesn't exist, try to create one
        if (error.code === 'PGRST116') {
          return await createDefaultProfile(userId, userEmail)
        }
        // For other errors, return in-memory profile
        return createInMemoryProfile(userId, userEmail)
      }

      return data
    } catch (err) {
      // If anything fails, return in-memory profile
      return createInMemoryProfile(userId, userEmail)
    }
  }

  // Create in-memory profile as fallback
  const createInMemoryProfile = (userId, userEmail) => {
    return {
      id: userId,
      username: userEmail?.split('@')[0] || 'user',
      posting_points: 5,
      avatar_url: null,
      bio: null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      _isInMemory: true // Flag to indicate this is not from database
    }
  }

  // Create default profile for new users
  const createDefaultProfile = async (userId, userEmail) => {
    try {
      const defaultProfile = {
        id: userId,
        username: userEmail?.split('@')[0] || 'user',
        posting_points: 5,
        avatar_url: null,
        bio: null
      }

      const { data, error } = await supabase
        .from('profiles')
        .insert([defaultProfile])
        .select()
        .single()

      if (error) {
        // If database insert fails, return in-memory profile
        return createInMemoryProfile(userId, userEmail)
      }

      return data
    } catch (err) {
      // If anything fails, return in-memory profile
      return createInMemoryProfile(userId, userEmail)
    }
  }

  // Initialize auth state
  useEffect(() => {
    let mounted = true

    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession()

        if (error) {
          setError(error.message)
        } else if (session?.user && mounted) {
          setUser(session.user)
          const userProfile = await fetchUserProfile(session.user.id, session.user.email)
          if (mounted) {
            setProfile(userProfile)
          }
        }
      } catch (err) {
        if (mounted) {
          setError(err.message)
        }
      } finally {
        if (mounted) {
          setLoading(false)
        }
      }
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (_, session) => {
      if (!mounted) return

      try {
        if (session?.user) {
          setUser(session.user)
          const userProfile = await fetchUserProfile(session.user.id, session.user.email)
          if (mounted) {
            setProfile(userProfile)
          }
        } else {
          setUser(null)
          setProfile(null)
        }
      } catch (err) {
        setError(err.message)
      } finally {
        if (mounted) {
          setLoading(false)
          setError(null)
        }
      }
    })

    // Cleanup function
    return () => {
      mounted = false
      subscription?.unsubscribe()
    }
  }, [])

  // Sign up function
  const signUp = async (email, password, username) => {
    try {
      setLoading(true)
      setError(null)

      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            username: username || `user_${Date.now()}`
          }
        }
      })

      if (error) {
        setError(error.message)
        return { success: false, error: error.message }
      }

      return { success: true, data }
    } catch (err) {
      const errorMessage = err.message || '发生了意外错误'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }

  // Sign in function
  const signIn = async (email, password) => {
    try {
      setLoading(true)
      setError(null)

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      if (error) {
        setError(error.message)
        return { success: false, error: error.message }
      }

      return { success: true, data }
    } catch (err) {
      const errorMessage = err.message || '发生了意外错误'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }

  // Sign out function
  const signOut = async () => {
    try {
      setLoading(true)
      const { error } = await supabase.auth.signOut()
      
      if (error) {
        setError(error.message)
        return { success: false, error: error.message }
      }

      setUser(null)
      setProfile(null)
      return { success: true }
    } catch (err) {
      const errorMessage = err.message || '发生了意外错误'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }

  // Update profile function
  const updateProfile = async (updates) => {
    try {
      if (!user) {
        throw new Error('用户未登录')
      }

      const { data, error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', user.id)
        .select()
        .single()

      if (error) {
        setError(error.message)
        return { success: false, error: error.message }
      }

      setProfile(data)
      return { success: true, data }
    } catch (err) {
      const errorMessage = err.message || '发生了意外错误'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    }
  }

  // Reset password function
  const resetPassword = async (email) => {
    try {
      setLoading(true)
      setError(null)

      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`
      })

      if (error) {
        setError(error.message)
        return { success: false, error: error.message }
      }

      return { success: true }
    } catch (err) {
      const errorMessage = err.message || '发生了意外错误'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }

  // Context value
  const isAuthenticated = !!user

  const value = {
    user,
    profile,
    loading,
    error,
    signUp,
    signIn,
    signOut,
    updateProfile,
    resetPassword,
    isAuthenticated,
    postingPoints: profile?.posting_points || 0
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export default AuthContext
