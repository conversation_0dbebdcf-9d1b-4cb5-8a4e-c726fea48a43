"""
Google Cloud Function for comment approval system
Provides secure backend logic for approving comments and rewarding users
"""

import os
import json
import logging
from typing import Dict, Any
from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from supabase import create_client, Client
import functions_framework
from google.cloud import logging as cloud_logging

# Initialize Google Cloud Logging
cloud_logging_client = cloud_logging.Client()
cloud_logging_client.setup_logging()

# Initialize Supabase client
def get_supabase_client() -> Client:
    """Initialize Supabase client with service role key for admin operations"""
    url = os.environ.get("SUPABASE_URL")
    service_role_key = os.environ.get("SUPABASE_SERVICE_ROLE_KEY")
    
    if not url or not service_role_key:
        raise ValueError("Missing required environment variables: SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY")
    
    return create_client(url, service_role_key)

# Initialize FastAPI app
app = FastAPI(title="Comment Approval API", version="1.0.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify your domain
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

def verify_auth_token(request: Request) -> str:
    """
    Verify the authentication token and return user ID
    In production, this should validate the JWT token from Supabase
    """
    auth_header = request.headers.get("Authorization")
    if not auth_header or not auth_header.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Missing or invalid authorization header")
    
    token = auth_header.replace("Bearer ", "")
    
    # For now, we'll extract user ID from the token
    # In production, you should validate the JWT token properly
    try:
        supabase = get_supabase_client()
        # Verify the token with Supabase
        user = supabase.auth.get_user(token)
        if not user or not user.user:
            raise HTTPException(status_code=401, detail="Invalid token")
        return user.user.id
    except Exception as e:
        logging.error(f"Token verification failed: {str(e)}")
        raise HTTPException(status_code=401, detail="Token verification failed")

@app.get("/")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "comment-approval-api"}

@app.post("/approve-comment")
async def approve_comment(request: Request):
    """
    Approve a comment and reward the commenter
    Only the post author can approve comments on their posts
    """
    try:
        # Get request body
        body = await request.json()
        comment_id = body.get("comment_id")
        
        if not comment_id:
            raise HTTPException(status_code=400, detail="comment_id is required")
        
        # Verify authentication (commented out for now since we're using direct DB function)
        # user_id = verify_auth_token(request)
        
        # Initialize Supabase client
        supabase = get_supabase_client()
        
        # Get comment and post information for validation
        comment_response = supabase.table("comments").select(
            "*, posts(author_id)"
        ).eq("id", comment_id).single().execute()
        
        if not comment_response.data:
            raise HTTPException(status_code=404, detail="Comment not found")
        
        comment_data = comment_response.data
        post_author_id = comment_data["posts"]["author_id"]
        
        # For now, we'll skip user verification since we're calling this from the frontend
        # In production, you should verify that the calling user is the post author
        # if user_id != post_author_id:
        #     raise HTTPException(status_code=403, detail="Only the post author can approve comments")
        
        # Call the database function to approve comment and reward user
        result = supabase.rpc('approve_and_reward', {'comment_id_to_approve': comment_id}).execute()
        
        if result.error:
            logging.error(f"Database function error: {result.error}")
            raise HTTPException(status_code=500, detail=f"Database error: {result.error}")
        
        logging.info(f"Comment {comment_id} approved successfully")
        
        return {
            "message": "Comment approved and points awarded successfully",
            "comment_id": comment_id,
            "success": True
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Unexpected error in approve_comment: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.get("/comment/{comment_id}")
async def get_comment(comment_id: int):
    """Get comment details (for testing purposes)"""
    try:
        supabase = get_supabase_client()
        
        response = supabase.table("comments").select(
            "*, profiles:commenter_id(username), posts(title, author_id)"
        ).eq("id", comment_id).single().execute()
        
        if not response.data:
            raise HTTPException(status_code=404, detail="Comment not found")
        
        return response.data
        
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error getting comment: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

# Google Cloud Functions entry point
@functions_framework.http
def main(request):
    """
    Google Cloud Functions entry point
    Routes all requests to the FastAPI app
    """
    import asyncio
    from fastapi.testclient import TestClient
    
    # For Cloud Functions, we need to handle the request differently
    if request.method == "OPTIONS":
        # Handle CORS preflight
        headers = {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
            'Access-Control-Max-Age': '3600'
        }
        return ('', 204, headers)
    
    # Use TestClient to handle the request
    with TestClient(app) as client:
        # Convert Cloud Functions request to FastAPI format
        headers = dict(request.headers)
        
        if request.method == "GET":
            response = client.get(request.path, headers=headers, params=request.args)
        elif request.method == "POST":
            try:
                json_data = request.get_json() if request.is_json else None
            except:
                json_data = None
            response = client.post(request.path, headers=headers, json=json_data)
        else:
            return ("Method not allowed", 405)
        
        # Convert FastAPI response to Cloud Functions format
        headers = dict(response.headers)
        headers['Access-Control-Allow-Origin'] = '*'
        
        return (response.content, response.status_code, headers)

if __name__ == "__main__":
    # For local development
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
