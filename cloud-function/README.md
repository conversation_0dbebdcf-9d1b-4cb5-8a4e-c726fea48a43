# Comment Approval Google Cloud Function

This Google Cloud Function provides secure backend logic for the comment approval system. It allows post authors to approve comments and automatically rewards commenters with posting points.

## Features

- **Secure Authentication**: Validates requests using Supabase JWT tokens
- **Atomic Operations**: Uses database functions to ensure data consistency
- **CORS Support**: Configured for cross-origin requests from your frontend
- **Error Handling**: Comprehensive error handling and logging
- **FastAPI Framework**: Modern, fast Python web framework

## Prerequisites

1. **Google Cloud Account**: Set up a Google Cloud project
2. **gcloud CLI**: Install and authenticate with Google Cloud
3. **Supabase Project**: Running Supabase instance with the comment approval database function

## Environment Variables

Set these environment variables before deployment:

```bash
export SUPABASE_URL="https://your-project.supabase.co"
export SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"
```

⚠️ **Important**: Use the **service role key**, not the anon key, as this function needs admin privileges to execute database functions.

## Deployment

### Option 1: Using the deployment script

```bash
# Set environment variables
export SUPABASE_URL="https://your-project.supabase.co"
export SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"

# Run deployment script
./deploy.sh
```

### Option 2: Manual deployment

```bash
gcloud functions deploy comment-approval-api \
  --runtime python311 \
  --trigger-http \
  --allow-unauthenticated \
  --entry-point main \
  --memory 256MB \
  --timeout 60s \
  --region us-central1 \
  --set-env-vars SUPABASE_URL="$SUPABASE_URL",SUPABASE_SERVICE_ROLE_KEY="$SUPABASE_SERVICE_ROLE_KEY" \
  --source .
```

## API Endpoints

### POST /approve-comment

Approve a comment and reward the commenter.

**Request Body:**
```json
{
  "comment_id": 123
}
```

**Response:**
```json
{
  "message": "Comment approved and points awarded successfully",
  "comment_id": 123,
  "success": true
}
```

### GET /comment/{comment_id}

Get comment details (for testing).

**Response:**
```json
{
  "id": 123,
  "content": "Great article!",
  "is_author_approved": true,
  "profiles": {
    "username": "john_doe"
  },
  "posts": {
    "title": "My Article",
    "author_id": "user-uuid"
  }
}
```

### GET /

Health check endpoint.

**Response:**
```json
{
  "status": "healthy",
  "service": "comment-approval-api"
}
```

## Testing

### Local Development

```bash
# Install dependencies
pip install -r requirements.txt

# Set environment variables
export SUPABASE_URL="https://your-project.supabase.co"
export SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"

# Run locally
python main.py
```

The API will be available at `http://localhost:8000`

### Test the deployed function

```bash
# Health check
curl https://REGION-PROJECT.cloudfunctions.net/comment-approval-api

# Approve a comment
curl -X POST https://REGION-PROJECT.cloudfunctions.net/comment-approval-api/approve-comment \
  -H "Content-Type: application/json" \
  -d '{"comment_id": 1}'
```

## Frontend Integration

Update your React frontend to call the cloud function instead of the local Supabase function:

```javascript
// In your Comment component or API service
const CLOUD_FUNCTION_URL = "https://REGION-PROJECT.cloudfunctions.net/comment-approval-api";

const approveComment = async (commentId) => {
  try {
    const response = await fetch(`${CLOUD_FUNCTION_URL}/approve-comment`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Add authentication header if needed
        // 'Authorization': `Bearer ${userToken}`
      },
      body: JSON.stringify({ comment_id: commentId })
    });

    if (!response.ok) {
      throw new Error('Failed to approve comment');
    }

    const result = await response.json();
    return { data: result, error: null };
  } catch (error) {
    return { data: null, error: error.message };
  }
};
```

## Security Considerations

1. **Authentication**: Currently simplified for demo. In production, implement proper JWT token validation.
2. **CORS**: Configure specific origins instead of allowing all (`*`)
3. **Rate Limiting**: Consider implementing rate limiting for production use
4. **Input Validation**: Add more comprehensive input validation
5. **Logging**: Ensure sensitive data is not logged

## Troubleshooting

### Common Issues

1. **Permission Denied**: Ensure your service role key has the necessary permissions
2. **Function Timeout**: Increase timeout if database operations are slow
3. **CORS Errors**: Check CORS configuration in the function
4. **Environment Variables**: Verify all required environment variables are set

### Logs

View function logs:
```bash
gcloud functions logs read comment-approval-api --region us-central1
```

## Cost Optimization

- **Memory**: Function uses 256MB, adjust based on actual usage
- **Timeout**: Set to 60s, reduce if operations are faster
- **Cold Starts**: Consider using Cloud Run for better performance if needed

## Next Steps

1. Implement proper JWT token validation
2. Add rate limiting
3. Set up monitoring and alerting
4. Configure specific CORS origins
5. Add comprehensive tests
