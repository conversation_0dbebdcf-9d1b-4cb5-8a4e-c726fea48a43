#!/bin/bash

# Google Cloud Function Deployment Script
# Make sure you have gcloud CLI installed and authenticated

# Configuration
FUNCTION_NAME="comment-approval-api"
REGION="us-central1"  # Change to your preferred region
RUNTIME="python311"
ENTRY_POINT="main"
MEMORY="256MB"
TIMEOUT="60s"

# Environment variables (set these in your environment or modify here)
# SUPABASE_URL="your-supabase-url"
# SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"

echo "Deploying Google Cloud Function: $FUNCTION_NAME"

# Deploy the function
gcloud functions deploy $FUNCTION_NAME \
  --runtime $RUNTIME \
  --trigger-http \
  --allow-unauthenticated \
  --entry-point $ENTRY_POINT \
  --memory $MEMORY \
  --timeout $TIMEOUT \
  --region $REGION \
  --set-env-vars SUPABASE_URL="$SUPABASE_URL",SUPABASE_SERVICE_ROLE_KEY="$SUPABASE_SERVICE_ROLE_KEY" \
  --source .

if [ $? -eq 0 ]; then
    echo "✅ Deployment successful!"
    echo "Function URL: https://$REGION-$(gcloud config get-value project).cloudfunctions.net/$FUNCTION_NAME"
    echo ""
    echo "Test the function:"
    echo "curl https://$REGION-$(gcloud config get-value project).cloudfunctions.net/$FUNCTION_NAME"
else
    echo "❌ Deployment failed!"
    exit 1
fi
